import { createContext, useContext, useEffect, useState } from "react";

const backendUrl = import.meta.env.VITE_API_URL || "http://localhost:3000";

const ChatContext = createContext();

export const ChatProvider = ({ children }) => {
  const [messages, setMessages] = useState([]);
  const [message, setMessage] = useState();
  const [loading, setLoading] = useState(false);
  const [cameraZoomed, setCameraZoomed] = useState(true);
  const [error, setError] = useState(null);
  const [rateLimited, setRateLimited] = useState(false);

  const clearError = () => {
    setError(null);
    setRateLimited(false);
  };

  const chat = async (message) => {
    setLoading(true);
    setError(null);
    setRateLimited(false);

    try {
      const response = await fetch(`${backendUrl}/chat`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ message }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Validate response structure
      if (!data || !data.messages) {
        throw new Error('Invalid response format: missing messages array');
      }

      const resp = data.messages;

      // Ensure resp is an array
      if (!Array.isArray(resp)) {
        throw new Error('Invalid response format: messages is not an array');
      }

      // Validate each message has required properties
      const validMessages = resp.filter(msg =>
        msg &&
        typeof msg.text === 'string' &&
        typeof msg.facialExpression === 'string' &&
        typeof msg.animation === 'string'
      );

      if (validMessages.length === 0) {
        throw new Error('No valid messages received from server');
      }

      setMessages((messages) => [...messages, ...validMessages]);

    } catch (error) {
      console.error('Chat error:', error);

      // Fallback mode - provide demo responses when backend is unavailable
      if (error.message.includes('Failed to fetch') || error.message.includes('ERR_CONNECTION_REFUSED')) {
        console.log('Backend unavailable, using demo mode');

        const demoResponses = [
          {
            text: "Hi there! I'm currently in demo mode since the backend server isn't running.",
            facialExpression: "smile",
            animation: "Idle",
            audio: "",
            lipsync: { mouthCues: [] }
          },
          {
            text: "The PropertyBinding fixes are working great though! No more console errors.",
            facialExpression: "laugh",
            animation: "Idle",
            audio: "",
            lipsync: { mouthCues: [] }
          },
          {
            text: "To enable full functionality, please start the backend server on port 3000.",
            facialExpression: "default",
            animation: "Idle",
            audio: "",
            lipsync: { mouthCues: [] }
          }
        ];

        // Pick a random demo response
        const randomResponse = demoResponses[Math.floor(Math.random() * demoResponses.length)];
        setMessages((messages) => [...messages, randomResponse]);

        setError({
          type: 'demo_mode',
          message: 'Running in demo mode - backend server not available',
          isTemporary: false
        });
      } else if (error.message.includes('500')) {
        setError({
          type: 'server_error',
          message: 'Server is experiencing issues. Please try again later.',
          isTemporary: true
        });
      } else {
        setError({
          type: 'general_error',
          message: 'Something went wrong. Please try again.',
          isTemporary: true
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const onMessagePlayed = () => {
    setMessages((messages) => messages.slice(1));
  };

  useEffect(() => {
    if (messages.length > 0) {
      setMessage(messages[0]);
    } else {
      setMessage(null);
    }
  }, [messages]);

  return (
    <ChatContext.Provider
      value={{
        chat,
        message,
        onMessagePlayed,
        loading,
        cameraZoomed,
        setCameraZoomed,
        error,
        rateLimited,
        clearError,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

export const useChat = () => {
  const context = useContext(ChatContext);
  if (!context) {
    throw new Error("useChat must be used within a ChatProvider");
  }
  return context;
};
