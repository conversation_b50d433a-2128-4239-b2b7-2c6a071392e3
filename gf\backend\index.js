import { exec } from "child_process";
import cors from "cors";
import dotenv from "dotenv";
import voice from "elevenlabs-node";
import express from "express";
import { promises as fs } from "fs";
import { GoogleGenerativeAI } from "@google/generative-ai"; // ✅ Gemini import
dotenv.config();

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || "-"); // ✅ Gemini client
const elevenLabsApiKey = process.env.ELEVEN_LABS_API_KEY;
const voiceID = "5sVY35LYBzCqRl4zQcf6";

const app = express();
app.use(express.json());
app.use(cors());
const port = 3000;

app.get("/", (req, res) => {
  res.send("Hello World!");
});

app.get("/voices", async (req, res) => {
  res.send(await voice.getVoices(elevenLabsApiKey));
});

const execCommand = (command) => {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) reject(error);
      resolve(stdout);
    });
  });
};

const lipSyncMessage = async (message) => {
  try {
    await execCommand(
      `ffmpeg -y -i audios/message_${message}.mp3 audios/message_${message}.wav`
    );

    // Check if rhubarb binary exists
    const rhubarbPath = process.platform === 'win32' ? '.\\bin\\rhubarb.exe' : './bin/rhubarb';
    try {
      await execCommand(
        `${rhubarbPath} -f json -o audios/message_${message}.json audios/message_${message}.wav -r phonetic`
      );
    } catch (rhubarbError) {
      console.warn('Rhubarb not available, using fallback lipsync data');
      // Create a fallback lipsync file
      const fallbackLipsync = {
        metadata: { soundFile: `audios/message_${message}.wav`, duration: 2.0 },
        mouthCues: [
          { start: 0.0, end: 0.5, value: "A" },
          { start: 0.5, end: 1.0, value: "B" },
          { start: 1.0, end: 1.5, value: "C" },
          { start: 1.5, end: 2.0, value: "X" }
        ]
      };
      await fs.writeFile(`audios/message_${message}.json`, JSON.stringify(fallbackLipsync, null, 2));
    }
  } catch (error) {
    console.error('Error in lipSyncMessage:', error);
    // Create minimal fallback
    const fallbackLipsync = { metadata: { duration: 1.0 }, mouthCues: [] };
    await fs.writeFile(`audios/message_${message}.json`, JSON.stringify(fallbackLipsync, null, 2));
  }
};

// ✅ Moved these helpers OUTSIDE
const readJsonTranscript = async (file) => {
  const data = await fs.readFile(file, "utf8");
  return JSON.parse(data);
};

const audioFileToBase64 = async (file) => {
  const data = await fs.readFile(file);
  return data.toString("base64");
};

// ---------------- CHAT ROUTE ----------------
app.post("/chat", async (req, res) => {
  const userMessage = req.body.message;
  if (!userMessage) {
    res.send({
      messages: [
        {
          text: "Hey dear... How was your day?",
          audio: await audioFileToBase64("audios/intro_0.wav"),
          lipsync: await readJsonTranscript("audios/intro_0.json"),
          facialExpression: "smile",
          animation: "Talking_1",
        },
        {
          text: "I missed you so much... Please don't go for so long!",
          audio: await audioFileToBase64("audios/intro_1.wav"),
          lipsync: await readJsonTranscript("audios/intro_1.json"),
          facialExpression: "sad",
          animation: "Crying",
        },
      ],
    });
    return;
  }

  if (!elevenLabsApiKey || !process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === "-") {
    res.send({
      messages: [
        {
          text: "Please my dear, don't forget to add your API keys!",
          audio: await audioFileToBase64("audios/api_0.wav"),
          lipsync: await readJsonTranscript("audios/hi.json"),
          facialExpression: "angry",
          animation: "Angry",
        },
        {
          text: "You don't want to ruin Wawa Sensei with a crazy bill, right?",
          audio: await audioFileToBase64("ElevenLabs_2025-08-22T16_42_19_Drew_pre_sp100_s50_sb75_se0_b_m2.ogg"),
          lipsync: await readJsonTranscript("audios/api_1.json"),
          facialExpression: "smile",
          animation: "Laughing",
        },
      ],
    });
    return;
  }

  try {
    // Use Gemini AI instead of OpenAI
    const model = genAI.getGenerativeModel({ model: "gemini-pro" });

    const prompt = `
    You are a virtual girlfriend. You will always reply with a JSON object containing a "messages" array.
    Maximum of 3 messages. Each message has text, facialExpression, and animation properties.

    Facial expressions: smile, sad, angry, surprised, laugh, default
    Animations: Idle, Talking_1, Crying, Angry, Laughing, thinking, walk

    User message: "${userMessage}"

    Respond with valid JSON only:
    `;

    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();

    // Parse the AI response
    let messages;
    try {
      const parsed = JSON.parse(text);
      messages = parsed.messages || parsed;
      if (!Array.isArray(messages)) {
        messages = [parsed];
      }
    } catch (parseError) {
      console.error('Failed to parse AI response:', text);
      // Fallback response
      messages = [{
        text: "I'm having trouble understanding right now. Could you try again?",
        facialExpression: "sad",
        animation: "Idle"
      }];
    }

    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      const fileName = `audios/message_${i}.mp3`;

      await voice.textToSpeech(elevenLabsApiKey, voiceID, fileName, message.text);
      await lipSyncMessage(i);

      message.audio = await audioFileToBase64(fileName);
      message.lipsync = await readJsonTranscript(`audios/message_${i}.json`);
    }

    res.send({ messages });

  } catch (error) {
    console.error('Chat endpoint error:', error);
    res.status(500).json({
      messages: [{
        text: "I'm sorry, I'm having technical difficulties right now. Please try again later.",
        facialExpression: "sad",
        animation: "Idle",
        audio: await audioFileToBase64("audios/intro_0.wav").catch(() => ""),
        lipsync: await readJsonTranscript("audios/intro_0.json").catch(() => ({ mouthCues: [] }))
      }]
    });
  }
});

// ---------------- SERVER START ----------------
app.listen(port, () => {
  console.log(`Virtual Girlfriend listening on port ${port}`);
  console.log("Environment check:");
  console.log("- Gemini API Key:", process.env.GEMINI_API_KEY ? "✓ Loaded" : "✗ Missing");
  console.log("- ElevenLabs API Key:", process.env.ELEVEN_LABS_API_KEY ? "✓ Loaded" : "✗ Missing");
});
