import express from "express";
import cors from "cors";

const app = express();
app.use(express.json());
app.use(cors());
const port = 3000;

app.get("/", (req, res) => {
  res.send("Test server is running!");
});

app.post("/chat", async (req, res) => {
  console.log("Received chat request:", req.body);
  
  // Simple test response
  res.json({
    messages: [{
      text: "Hello! This is a test response.",
      facialExpression: "smile",
      animation: "Idle",
      audio: "",
      lipsync: { mouthCues: [] }
    }]
  });
});

app.listen(port, () => {
  console.log(`Test server listening on port ${port}`);
});
