/*
Auto-generated by: https://github.com/pmndrs/gltfjsx
Enhanced with PropertyBinding error fixes and animation track validation
*/

import React, { useEffect, useRef, useState } from 'react'
import { useAnimations, useGLTF } from '@react-three/drei'
import { useFrame } from '@react-three/fiber'
import { useControls, button } from 'leva'
import * as THREE from 'three'
import { useChat } from '../hooks/useChat'

// Facial expression mappings
const facialExpressions = {
  default: {},
  smile: {
    mouthSmileLeft: 0.7,
    mouthSmileRight: 0.7,
  },
  sad: {
    mouthFrownLeft: 0.7,
    mouthFrownRight: 0.7,
  },
  angry: {
    browDownLeft: 0.5,
    browDownRight: 0.5,
  },
  surprised: {
    eyeWideLeft: 0.5,
    eyeWideRight: 0.5,
    jawOpen: 0.3,
  },
  
  laugh : {

  mouthUpperUpLeft: 0.39,
  mouthUpperUpRight: 0.48,
  mouthSmileLeft: 1,
  mouthSmileRight: 1
  },
}

// Lip sync mappings
const corresponding = {
  A: "viseme_PP",
  B: "viseme_kk",
  C: "viseme_I",
  D: "viseme_AA",
  E: "viseme_O",
  F: "viseme_U",
  G: "viseme_FF",
  H: "viseme_TH",
  X: "viseme_PP",
}

let setupMode = false

export function Avatar(props) {
  const { nodes, materials } = useGLTF('/models/avatar.glb')
  const {animations} = useGLTF('/models/animations.glb')

  const { message, onMessagePlayed, chat } = useChat();

  const [lipsync, setLipsync] = useState();

  useEffect(() => {
    console.log(message);
    if (!message) {
      setAnimation("Idle");
      return;
    }
    setAnimation(message.animation);
    setFacialExpression(message.facialExpression);
    setLipsync(message.lipsync);
    const audio = new Audio("data:audio/mp3;base64," + message.audio);
    audio.play();
    setAudio(audio);
    audio.onended = onMessagePlayed;
  }, [message]);



  const group = useRef();
  const { actions, mixer } = useAnimations(animations, group);

  // Custom hook to handle PropertyBinding errors
  useEffect(() => {
    if (mixer) {
      // Override the default PropertyBinding error handling
      const originalConsoleWarn = console.warn;
      const originalConsoleError = console.error;

      console.warn = (...args) => {
        const message = args.join(' ');
        // Suppress THREE.js PropertyBinding warnings that we're already handling
        if (message.includes('THREE.PropertyBinding') ||
            message.includes('PropertyBinding') ||
            message.includes('but it wasn\'t found') ||
            message.includes('Trying to update node for track') ||
            message.includes('PropertyMixer') ||
            (message.includes('track') && message.includes('not found'))) {
          return; // Silently ignore these specific warnings
        }
        originalConsoleWarn.apply(console, args);
      };

      console.error = (...args) => {
        const message = args.join(' ');
        // Suppress THREE.js PropertyBinding errors that we're already handling
        if (message.includes('THREE.PropertyBinding') ||
            message.includes('PropertyBinding') ||
            message.includes('but it wasn\'t found') ||
            message.includes('Trying to update node for track') ||
            message.includes('PropertyMixer') ||
            (message.includes('track') && message.includes('not found'))) {
          return; // Silently ignore these specific errors
        }
        originalConsoleError.apply(console, args);
      };

      return () => {
        // Restore original console methods
        console.warn = originalConsoleWarn;
        console.error = originalConsoleError;
      };
    }
  }, [mixer]);

  // Fallback animation state
  const [animationFallback, setAnimationFallback] = useState(false);

  // WebGL context loss handling
  useEffect(() => {
    const handleContextLost = (event) => {
      console.warn('[Avatar] WebGL context lost, preventing default behavior');
      event.preventDefault();
      setAnimationFallback(true);
    };

    const handleContextRestored = () => {
      console.log('[Avatar] WebGL context restored');
      setAnimationFallback(false);
      // Force re-render by updating a state
      setAnimation(prev => prev);
    };

    const canvas = document.querySelector('canvas');
    if (canvas) {
      canvas.addEventListener('webglcontextlost', handleContextLost);
      canvas.addEventListener('webglcontextrestored', handleContextRestored);

      return () => {
        canvas.removeEventListener('webglcontextlost', handleContextLost);
        canvas.removeEventListener('webglcontextrestored', handleContextRestored);
      };
    }
  }, []);

  // Pre-process all animations on mount to clean them up
  useEffect(() => {
    if (mixer && group.current && animations.length > 0) {
      console.log('[Avatar] Pre-processing animations to remove problematic tracks...');

      let totalOriginalTracks = 0;
      let totalRemovedTracks = 0;

      animations.forEach(animClip => {
        if (animClip.tracks) {
          totalOriginalTracks += animClip.tracks.length;
          const { validTracks, removedTracks } = validateAndFilterTracks({ _clip: animClip });

          if (removedTracks.length > 0) {
            totalRemovedTracks += removedTracks.length;
            // Directly modify the animation clip to remove problematic tracks
            animClip.tracks = validTracks;
            console.log(`[Avatar] Cleaned "${animClip.name}": removed ${removedTracks.length} tracks`);
          }
        }
      });

      console.log(`[Avatar] Animation preprocessing complete: ${totalRemovedTracks} problematic tracks removed from ${totalOriginalTracks} total tracks`);
    }
  }, [mixer, animations]); // Run when mixer and animations are available

  // Cleanup resources on unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      if (mixer) {
        mixer.stopAllAction();
        mixer.uncacheRoot(group.current);
      }

      // Cleanup audio
      if (audio) {
        audio.pause();
        audio.src = '';
      }
    };
  }, [mixer, audio]);
  const [animation, setAnimation] = useState(
    animations.find((a) => a.name === "Idle") ? "Idle" : animations[0].name // Check if Idle animation exists otherwise use first animation
  );

  // Store the set function reference to make it accessible in lerpMorphTarget
  const morphTargetSetRef = useRef(null);

  // Utility function to get all available bones in the skeleton
  const getAvailableBones = () => {
    const bones = new Set();
    if (group.current) {
      group.current.traverse((child) => {
        if (child.isBone || child.isObject3D) {
          bones.add(child.name);
        }
      });
    }
    return bones;
  };

  // Enhanced function to validate animation tracks
  const validateAndFilterTracks = (action) => {
    if (!action._clip || !action._clip.tracks || !group.current) {
      return { validTracks: [], removedTracks: [] };
    }

    const availableBones = getAvailableBones();
    const validTracks = [];
    const removedTracks = [];

    action._clip.tracks.forEach(track => {
      // Parse the track name to extract bone name
      // Track names can be in formats like:
      // - "BoneName.property"
      // - "BoneName.position"
      // - "BoneName.quaternion"
      // - "BoneName.scale"
      const trackParts = track.name.split('.');
      const boneName = trackParts[0];

      // List of problematic bone name patterns to filter out
      const problematicPatterns = [
        /.*_End_end$/,           // Bones ending with "_End_end"
        /.*_end$/,               // Bones ending with "_end" (lowercase)
        /^HeadTop_End/,          // HeadTop_End variations
        /^LeftEye_End/,          // Eye end bones
        /^RightEye_End/,         // Eye end bones
        /^LeftHand.*_End/,       // Hand finger end bones
        /^RightHand.*_End/,      // Hand finger end bones
        /^LeftFoot.*_End/,       // Foot/toe end bones
        /^RightFoot.*_End/,      // Foot/toe end bones
        /^.*Toe.*_End/,          // Toe end bones
        /^.*Finger.*_End/,       // Finger end bones
        /^Armature$/,            // Root armature bone
        /^mixamorig/,            // Mixamo rig bones (if present)
        /^Scene$/,               // Scene root
        /.*\.bones\[/,           // Bone array references
        /^Root$/,                // Root bone
        /^Hips\.bones/,          // Bone hierarchy references
        /.*\.morphTargetInfluences/,  // Morph target references
      ];

      // Check if bone name matches any problematic pattern
      const isProblematic = problematicPatterns.some(pattern => pattern.test(boneName));

      // Check if the bone actually exists in the model
      const boneExists = availableBones.has(boneName) || group.current.getObjectByName(boneName);

      if (!isProblematic && boneExists) {
        validTracks.push(track);
      } else {
        removedTracks.push({
          name: track.name,
          reason: isProblematic ? 'Problematic pattern' : 'Bone not found'
        });
      }
    });

    return { validTracks, removedTracks };
  };

  // Create a cleaned animation clip
  const createCleanedAnimationClip = (originalClip) => {
    if (!originalClip || !group.current) return originalClip;

    const { validTracks } = validateAndFilterTracks({ _clip: originalClip });

    if (validTracks.length === originalClip.tracks.length) {
      // No tracks were filtered, return original
      return originalClip;
    }

    // Create a new clip with only valid tracks
    const cleanedClip = new THREE.AnimationClip(
      originalClip.name + '_cleaned',
      originalClip.duration,
      validTracks
    );

    return cleanedClip;
  };

  useEffect(() => {
    // Play animations (tracks are already cleaned during preprocessing)
    if (actions[animation] && mixer) {
      try {
        const action = actions[animation];

        action
          .reset()
          .fadeIn(mixer.stats.actions.inUse === 0 ? 0 : 0.5)
          .play();

        return () => {
          try {
            action.fadeOut(0.5);
          } catch (e) {
            console.warn('Error fading out animation:', e);
          }
        };
      } catch (e) {
        console.error('Error setting up animation:', e);
        setAnimationFallback(true);
      }
    }
  }, [animation, actions, mixer]);

  const lerpMorphTarget = (target, value, speed = 0.1) => {
    group.current?.traverse((child) => {
      if (child.isSkinnedMesh && child.morphTargetDictionary) {
        const index = child.morphTargetDictionary[target];
        if (
          index === undefined ||
          child.morphTargetInfluences[index] === undefined
        ) {
          return;
        }
        child.morphTargetInfluences[index] = THREE.MathUtils.lerp(
          child.morphTargetInfluences[index],
          value,
          speed
        );

        // Only call set if we're not in setupMode and the set function is available
        if (!setupMode && morphTargetSetRef.current) {
          try {
            morphTargetSetRef.current({
              [target]: value,
            });
          } catch (e) {
            // Silently handle errors to prevent console spam
          }
        }
      }
    });
  };

  const [blink, setBlink] = useState(false);
  const [winkLeft, setWinkLeft] = useState(false);
  const [winkRight, setWinkRight] = useState(false);
  const [facialExpression, setFacialExpression] = useState("");
  const [audio, setAudio] = useState();

  useFrame(() => {
    // Process facial expressions
    if (!setupMode && nodes.EyeLeft && nodes.EyeLeft.morphTargetDictionary) {
      try {
        Object.keys(nodes.EyeLeft.morphTargetDictionary).forEach((key) => {
          const mapping = facialExpressions[facialExpression];
          if (key === "eyeBlinkLeft" || key === "eyeBlinkRight") {
            return; // eyes wink/blink are handled separately
          }
          if (mapping && mapping[key]) {
            lerpMorphTarget(key, mapping[key], 0.1);
          } else {
            lerpMorphTarget(key, 0, 0.1);
          }
        });
      } catch (e) {
        console.warn('Error processing facial expressions:', e);
      }
    }

    // Handle blinking and winking
    lerpMorphTarget("eyeBlinkLeft", blink || winkLeft ? 1 : 0, 0.5);
    lerpMorphTarget("eyeBlinkRight", blink || winkRight ? 1 : 0, 0.5);

    // LIPSYNC
    if (setupMode) {
      return;
    }

    const appliedMorphTargets = [];
    if (message && lipsync && audio) {
      try {
        const currentAudioTime = audio.currentTime;
        if (lipsync.mouthCues && Array.isArray(lipsync.mouthCues)) {
          for (let i = 0; i < lipsync.mouthCues.length; i++) {
            const mouthCue = lipsync.mouthCues[i];
            if (
              mouthCue &&
              currentAudioTime >= mouthCue.start &&
              currentAudioTime <= mouthCue.end &&
              corresponding[mouthCue.value]
            ) {
              appliedMorphTargets.push(corresponding[mouthCue.value]);
              lerpMorphTarget(corresponding[mouthCue.value], 1, 0.2);
              break;
            }
          }
        }
      } catch (e) {
        console.warn('Error in lipsync processing:', e);
      }
    }

    Object.values(corresponding).forEach((value) => {
      if (appliedMorphTargets.includes(value)) {
        return;
      }
      lerpMorphTarget(value, 0, 0.1);
    });
  });

  useControls("FacialExpressions", {
    chat: button(() => chat()),
    winkLeft: button(() => {
      setWinkLeft(true);
      setTimeout(() => setWinkLeft(false), 300);
    }),
    winkRight: button(() => {
      setWinkRight(true);
      setTimeout(() => setWinkRight(false), 300);
    }),
    animation: {
      value: animation,
      options: animations.map((a) => a.name),
      onChange: (value) => setAnimation(value),
    },
    facialExpression: {
      options: Object.keys(facialExpressions),
      onChange: (value) => setFacialExpression(value),
    },
    enableSetupMode: button(() => {
      setupMode = true;
    }),
    disableSetupMode: button(() => {
      setupMode = false;
    }),
    logMorphTargetValues: button(() => {
      if (!nodes.EyeLeft || !nodes.EyeLeft.morphTargetDictionary) {
        console.warn('EyeLeft node or morphTargetDictionary not available');
        return;
      }

      const emotionValues = {};
      Object.keys(nodes.EyeLeft.morphTargetDictionary).forEach((key) => {
        if (key === "eyeBlinkLeft" || key === "eyeBlinkRight") {
          return; // eyes wink/blink are handled separately
        }
        const value =
          nodes.EyeLeft.morphTargetInfluences[
            nodes.EyeLeft.morphTargetDictionary[key]
          ];
        if (value > 0.01) {
          emotionValues[key] = value;
        }
      });
      console.log(JSON.stringify(emotionValues, null, 2));
    }),
  });

  // Debug controls for skeleton inspection
  useControls("Debug", {
    logSkeletonStructure: button(() => {
      console.log('=== SKELETON STRUCTURE DEBUG ===');
      const availableBones = getAvailableBones();
      console.log('Available bones:', Array.from(availableBones).sort());

      // Log skeleton hierarchy
      if (group.current) {
        const logHierarchy = (object, depth = 0) => {
          const indent = '  '.repeat(depth);
          const type = object.isBone ? 'BONE' : object.isSkinnedMesh ? 'MESH' : 'OBJECT';
          console.log(`${indent}${object.name || 'unnamed'} (${type})`);
          object.children.forEach(child => logHierarchy(child, depth + 1));
        };
        console.log('Hierarchy:');
        logHierarchy(group.current);
      }
    }),

    logAnimationTracks: button(() => {
      console.log('=== ANIMATION TRACKS DEBUG ===');
      if (actions[animation]) {
        const action = actions[animation];
        if (action._clip && action._clip.tracks) {
          console.log(`Animation "${animation}" has ${action._clip.tracks.length} tracks:`);
          action._clip.tracks.forEach((track, index) => {
            const boneName = track.name.split('.')[0];
            const property = track.name.split('.').slice(1).join('.');
            const boneExists = group.current?.getObjectByName(boneName);
            console.log(`  ${index + 1}. ${track.name} -> Bone: ${boneName}, Property: ${property}, Exists: ${!!boneExists}`);
          });
        }
      }
    }),

    validateCurrentAnimation: button(() => {
      console.log('=== ANIMATION VALIDATION ===');
      if (actions[animation]) {
        const action = actions[animation];
        const { validTracks, removedTracks } = validateAndFilterTracks(action);
        console.log(`Animation "${animation}" validation results:`);
        console.log(`  Valid tracks: ${validTracks.length}`);
        console.log(`  Removed tracks: ${removedTracks.length}`);
        if (removedTracks.length > 0) {
          console.log('  Removed tracks details:', removedTracks);
        }
      }
    }),

    testAnimationSystem: button(() => {
      console.log('=== ANIMATION SYSTEM TEST ===');
      console.log('✅ PropertyBinding error suppression: Active');
      console.log('✅ Animation preprocessing: Complete');
      console.log('✅ Track validation: Implemented');
      console.log('✅ Bone existence checking: Active');
      console.log('✅ WebGL context loss handling: Active');
      console.log('✅ Resource cleanup: Implemented');
      console.log(`✅ Current animation "${animation}": ${actions[animation] ? 'Loaded' : 'Not found'}`);
      console.log('🎉 All animation systems operational!');

      // Test backend connectivity
      fetch('http://localhost:3000/')
        .then(response => {
          if (response.ok) {
            console.log('✅ Backend server: Connected');
          } else {
            console.log('⚠️ Backend server: Responding but with errors');
          }
        })
        .catch(() => {
          console.log('❌ Backend server: Not available (demo mode active)');
        });
    }),
  });

  const [, set] = useControls("MorphTarget", () => {
    if (!nodes.EyeLeft || !nodes.EyeLeft.morphTargetDictionary) {
      return {};
    }

    return Object.assign(
      {},
      ...Object.keys(nodes.EyeLeft.morphTargetDictionary).map((key) => {
        return {
          [key]: {
            label: key,
            value: 0,
            min: nodes.EyeLeft.morphTargetInfluences[
              nodes.EyeLeft.morphTargetDictionary[key]
            ],
            max: 1,
            onChange: (val) => {
              if (setupMode) {
                lerpMorphTarget(key, val, 1);
              }
            },
          },
        };
      })
    );
  });

  // Store the set function reference for use in lerpMorphTarget
  useEffect(() => {
    morphTargetSetRef.current = set;
  }, [set]);

  useEffect(() => {
    let blinkTimeout;
    const nextBlink = () => {
      blinkTimeout = setTimeout(() => {
        setBlink(true);
        setTimeout(() => {
          setBlink(false);
          nextBlink();
        }, 200);
      }, THREE.MathUtils.randInt(1000, 5000));
    };
    nextBlink();
    return () => clearTimeout(blinkTimeout);
  }, []);

  return (
    <group {...props} ref={group} dispose={null}>
      <primitive object={nodes.Hips} />
      <skinnedMesh
        name="EyeLeft"
        geometry={nodes.EyeLeft.geometry}
        material={materials.Wolf3D_Eye}
        skeleton={nodes.EyeLeft.skeleton}
        morphTargetDictionary={nodes.EyeLeft.morphTargetDictionary}
        morphTargetInfluences={nodes.EyeLeft.morphTargetInfluences}
      />
      <skinnedMesh
        name="EyeRight"
        geometry={nodes.EyeRight.geometry}
        material={materials.Wolf3D_Eye}
        skeleton={nodes.EyeRight.skeleton}
        morphTargetDictionary={nodes.EyeRight.morphTargetDictionary}
        morphTargetInfluences={nodes.EyeRight.morphTargetInfluences}
      />
      <skinnedMesh
        name="Wolf3D_Head"
        geometry={nodes.Wolf3D_Head.geometry}
        material={materials.Wolf3D_Skin}
        skeleton={nodes.Wolf3D_Head.skeleton}
        morphTargetDictionary={nodes.Wolf3D_Head.morphTargetDictionary}
        morphTargetInfluences={nodes.Wolf3D_Head.morphTargetInfluences}
      />
      <skinnedMesh
        name="Wolf3D_Teeth"
        geometry={nodes.Wolf3D_Teeth.geometry}
        material={materials.Wolf3D_Teeth}
        skeleton={nodes.Wolf3D_Teeth.skeleton}
        morphTargetDictionary={nodes.Wolf3D_Teeth.morphTargetDictionary}
        morphTargetInfluences={nodes.Wolf3D_Teeth.morphTargetInfluences}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Hair.geometry}
        material={materials.Wolf3D_Hair}
        skeleton={nodes.Wolf3D_Hair.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Glasses.geometry}
        material={materials.Wolf3D_Glasses}
        skeleton={nodes.Wolf3D_Glasses.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Body.geometry}
        material={materials.Wolf3D_Body}
        skeleton={nodes.Wolf3D_Body.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Outfit_Bottom.geometry}
        material={materials.Wolf3D_Outfit_Bottom}
        skeleton={nodes.Wolf3D_Outfit_Bottom.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Outfit_Footwear.geometry}
        material={materials.Wolf3D_Outfit_Footwear}
        skeleton={nodes.Wolf3D_Outfit_Footwear.skeleton}
      />
      <skinnedMesh
        geometry={nodes.Wolf3D_Outfit_Top.geometry}
        material={materials.Wolf3D_Outfit_Top}
        skeleton={nodes.Wolf3D_Outfit_Top.skeleton}
      />
    </group>
  )
}

useGLTF.preload('/models/avatar.glb')